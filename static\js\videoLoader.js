/**
 * Video Loader Module
 * Handles loading and displaying YouTube videos
 */

/**
 * Video data configuration
 */
const videoData = [
  {
    title: "OVER-THINKING",
    videoId: "Ct5kE8KGnQM",
    url: "https://www.youtube.com/watch?v=Ct5kE8KGnQM",
  },
  {
    title: "Childhood Nostalgia",
    videoId: "2jfeauEQx7w",
    url: "https://www.youtube.com/watch?v=2jfeauEQx7w",
  },
  {
    title: "This Perfect World",
    videoId: "kFSdn2X1Ttw",
    url: "https://www.youtube.com/watch?v=kFSdn2X1Ttw",
  },
  {
    title: "Tragic Ending",
    videoId: "uf6PZ9WisZQ",
    url: "https://www.youtube.com/watch?v=uf6PZ9WisZQ",
  },
];

/**
 * Creates a video element with iframe and link using Bootstrap 5 grid
 * @param {Object} video - Video object containing title, videoId, and url
 * @param {string} layoutType - Grid layout type to use (optional)
 * @returns {HTMLElement} - The created video element
 */
function createVideoElement(video, layoutType = 'responsive') {
  const colDiv = document.createElement("div");
  // Use the specified layout or default responsive layout
  const gridClass = gridLayouts[layoutType] || gridLayouts.responsive;
  colDiv.className = `${gridClass} mb-4`;

  colDiv.innerHTML = `
    <div class="card h-100 shadow-sm">
      <div class="ratio ratio-16x9">
        <iframe
          src="https://www.youtube.com/embed/${video.videoId}?si=dca-S4qIp2txXlSA"
          title="${video.title}"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          referrerpolicy="strict-origin-when-cross-origin"
          allowfullscreen
          class="rounded-top"
        ></iframe>
      </div>
      <div class="card-body d-flex flex-column">
        <h5 class="card-title">${video.title}</h5>
        <div class="mt-auto">
          <a
            rel="noopener"
            class="btn btn-primary btn-sm w-100"
            href="${video.url}"
            target="_blank"
          >
            <i class="bi bi-play-circle me-1"></i>
            Assistir no YouTube
          </a>
        </div>
      </div>
    </div>
  `;

  return colDiv;
}

/**
 * Loads and displays all videos in the video container using Bootstrap 5 grid
 * @param {string} layoutType - Grid layout type to use (optional)
 * @returns {void}
 */
function loadVideos(layoutType = 'responsive') {
  const videoContainer = document.getElementById("videoContainer");

  if (!videoContainer) {
    console.warn("videoContainer não encontrado.");
    return;
  }

  // Clear existing content
  videoContainer.innerHTML = '';

  // Create Bootstrap 5 grid structure
  const containerFluid = document.createElement("div");
  containerFluid.className = "container-fluid";

  const row = document.createElement("div");
  row.className = "row g-4"; // g-4 adds consistent gutters between columns

  // Add each video to the row with specified layout
  videoData.forEach((video) => {
    const videoElement = createVideoElement(video, layoutType);
    row.appendChild(videoElement);
  });

  containerFluid.appendChild(row);
  videoContainer.appendChild(containerFluid);
}

/**
 * Gets the video data array
 * @returns {Array} - Array of video objects
 */
function getVideoData() {
  return videoData;
}

/**
 * Adds a new video to the collection
 * @param {Object} video - Video object containing title, videoId, and url
 */
function addVideo(video) {
  if (video && video.title && video.videoId && video.url) {
    videoData.push(video);
  } else {
    console.error("Invalid video object provided");
  }
}

/**
 * Grid layout configuration options
 */
const gridLayouts = {
  // Responsive layout (default)
  responsive: "col-12 col-md-6 col-lg-4 col-xl-3",
  // Large cards - 2 per row on medium screens, 3 on large
  large: "col-12 col-md-6 col-lg-4",
  // Medium cards - 2 per row on small screens, 4 on large
  medium: "col-12 col-sm-6 col-lg-3",
  // Small cards - 3 per row on medium screens, 6 on large
  small: "col-12 col-sm-4 col-md-3 col-lg-2",
  // Single column layout
  single: "col-12"
};

/**
 * Sets the grid layout for video cards
 * @param {string} layoutType - Type of layout (responsive, large, medium, small, single)
 */
function setGridLayout(layoutType = 'responsive') {
  const layout = gridLayouts[layoutType] || gridLayouts.responsive;

  // Update existing video cards
  const videoCards = document.querySelectorAll('#videoContainer .col-12');
  videoCards.forEach(card => {
    card.className = `${layout} mb-4`;
  });
}

/**
 * Loads videos with a specific grid layout
 * @param {string} layoutType - Type of layout to use
 */
function loadVideosWithLayout(layoutType = 'responsive') {
  loadVideos();
  setGridLayout(layoutType);
}

/**
 * Gets available grid layout options
 * @returns {Object} - Object containing available layout types
 */
function getGridLayouts() {
  return Object.keys(gridLayouts);
}

// Export functions for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    loadVideos,
    getVideoData,
    addVideo,
    createVideoElement,
    setGridLayout,
    loadVideosWithLayout,
    getGridLayouts,
    gridLayouts
  };
}
