/**
 * Theme Manager Module
 * Handles light/dark theme switching functionality
 */

/**
 * Theme constants
 */
const THEMES = {
  LIGHT: 'light',
  DARK: 'dark'
};

/**
 * Gets the currently selected theme from localStorage
 * @returns {string} - The current theme (light or dark)
 */
function getCurrentTheme() {
  return localStorage.getItem("theme") || THEMES.LIGHT;
}

/**
 * Sets the theme for the website
 * @param {string} theme - The theme to set (light or dark)
 */
function setTheme(theme) {
  const body = document.body;
  const themeSwitcher = document.getElementById("theme-switcher");

  if (theme === THEMES.DARK) {
    body.classList.add("dark");
    if (themeSwitcher) {
      themeSwitcher.checked = true;
    }
  } else {
    body.classList.remove("dark");
    if (themeSwitcher) {
      themeSwitcher.checked = false;
    }
  }

  // Save theme preference
  localStorage.setItem("theme", theme);
}

/**
 * Toggles between light and dark themes
 */
function toggleTheme() {
  const currentTheme = getCurrentTheme();
  const newTheme = currentTheme === THEMES.DARK ? THEMES.LIGHT : THEMES.DARK;
  setTheme(newTheme);
}

/**
 * Initializes the theme system with saved preferences and sets up event listeners
 */
function initializeTheme() {
  const themeSwitcher = document.getElementById("theme-switcher");
  
  if (!themeSwitcher) {
    console.warn("Theme switcher element not found.");
    return;
  }

  // Set initial theme based on saved preference
  const savedTheme = getCurrentTheme();
  setTheme(savedTheme);

  // Set up event listener for theme switcher
  themeSwitcher.addEventListener("change", function () {
    const newTheme = this.checked ? THEMES.DARK : THEMES.LIGHT;
    setTheme(newTheme);
  });
}

/**
 * Checks if dark theme is currently active
 * @returns {boolean} - True if dark theme is active
 */
function isDarkTheme() {
  return getCurrentTheme() === THEMES.DARK;
}

/**
 * Applies theme-specific styles to an element
 * @param {HTMLElement} element - The element to style
 * @param {Object} lightStyles - Styles for light theme
 * @param {Object} darkStyles - Styles for dark theme
 */
function applyThemeStyles(element, lightStyles, darkStyles) {
  if (!element) return;

  const styles = isDarkTheme() ? darkStyles : lightStyles;
  
  Object.keys(styles).forEach(property => {
    element.style[property] = styles[property];
  });
}

/**
 * Gets theme-specific CSS class
 * @param {string} baseClass - Base CSS class name
 * @returns {string} - Theme-specific class name
 */
function getThemeClass(baseClass) {
  return isDarkTheme() ? `${baseClass}-dark` : `${baseClass}-light`;
}

/**
 * Sets up automatic theme detection based on system preference
 */
function setupSystemThemeDetection() {
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    // Only apply system theme if no user preference is saved
    if (!localStorage.getItem("theme")) {
      const systemTheme = mediaQuery.matches ? THEMES.DARK : THEMES.LIGHT;
      setTheme(systemTheme);
    }

    // Listen for system theme changes
    mediaQuery.addEventListener('change', (e) => {
      // Only apply if no user preference is saved
      if (!localStorage.getItem("theme")) {
        const systemTheme = e.matches ? THEMES.DARK : THEMES.LIGHT;
        setTheme(systemTheme);
      }
    });
  }
}

// Export functions for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    THEMES,
    getCurrentTheme,
    setTheme,
    toggleTheme,
    initializeTheme,
    isDarkTheme,
    applyThemeStyles,
    getThemeClass,
    setupSystemThemeDetection
  };
}
