/**
 * Main.js - Legacy compatibility file
 *
 * This file maintains backward compatibility while the functionality
 * has been moved to separate, more organized modules:
 * - videoLoader.js - Video loading functionality
 * - languageManager.js - Language switching functionality
 * - themeManager.js - Theme switching functionality
 * - app.js - Application initialization
 *
 * Note: This file is kept for compatibility but it's recommended to
 * use the new modular structure by including the individual modules.
 */

console.warn('main.js is deprecated. Please use the new modular structure with individual JS files.');

// This file is now empty as functionality has been moved to separate modules
// The app.js file handles the initialization that was previously done here
