/* Light Theme */
:root {
    --bg-color: #ffffff;
    --text-color: #000000;
    --shadow-color-d: rgba(0, 0, 0, 0.051);
    --shadow-color-l: rgba(255, 255, 255, 0.05);
}

/* Dark Theme */
body.dark {
    --bg-color: #000000;
    --text-color: #ffffff;
}

.btn-primary {
  --bs-btn-font-weight: 600;
  --bs-btn-color: var(--bs-white);
  --bs-btn-bg: red;
  --bs-btn-border-color: red;
  --bs-btn-hover-color: var(--bs-white);
  --bs-btn-hover-bg: hsl(0, 66%, 50%);
  --bs-btn-hover-border-color: hsl(0, 66%, 50%);
  --bs-btn-active-color: var(--bs-btn-hover-color);
}

.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;

    input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    input:checked+.slider {
        background-color: #000000, var(--text-color);
    }

    input:checked+.slider:before {
        transform: translateX(20px);
        background-color: transparent;
        border-radius: 50%;
        border-top-color: transparent;
        border-left-color: transparent;
        border-right-color: transparent;
        box-shadow: inset -5px -3px 0 #d8e9ef;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ffffff;
        transition: 0.4s;
        border-radius: 20px;
        box-shadow: 0 0 0.25em rgba(67, 71, 85, 0.27), 0.2px 0.2em 24px 0 rgba(1, 29, 77, 0.15);
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 4px;
        bottom: 4px;
        background-color: yellow;
        transition: 0.4s;
        border-radius: 50%;
        text-align: center;
        line-height: 16px;
    }

}


#up,
#down,
.page-link,
button {
    transition: all 0.3s ease;
}

svg {
    color: var(--text-color);
}

a {
    border-radius: 0.5rem;
    text-decoration: none;
}

.link {
    animation: fadeIn 7s ease-in-out;
}

#up,
#down {
    margin: 0;
    color: #ff0800;
    padding: 2rem;
}

#up:hover,
#down:hover {
    background-color: var(--bg-color);
    color: var(--text-color);
}

.container {
    background-color: var(--bg-color);
    color: var(--text-color);
}

h1,
h2,
h3,
h4,
h5,
p {
    text-align: center;
}

h1 {
    color: #ff0800;
    font-size: 3rem;
}

h2,
h3,
h4 {
    color: var(--text-color);
    font-size: 2rem;
}

h1,
h2,
h3,
h4,
h5 {
    animation: fadeIn 3s ease-in-out;
}

p.lead {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    animation: fadeIn 5s ease-in-out;
}

iframe {
    aspect-ratio: 16/9;
    animation: fadeIn 5s ease-in-out;
}

#youtube,
.video-links {
    text-align: center;
}

.video-links {
    padding: 1rem;
    font-size: 1rem;
    font-weight: bold;
}

.video-links a {
    padding: 0.5rem;
    border-radius: 1rem;
    color: #ff261d;
    transition: color, border 0.3s ease;
}

.video-links a:hover {
    border: 2px solid var(--text-color);
    color: var(--text-color);
}

.navbar {
    background-color: var(--bg-color);
    padding: 0.5rem;
}

.nav-link {
    background-color: var(--bg-color);
    color: var(--text-color);
    border: none;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: #ff0800;
    text-shadow: 0 0 3px #ff0800;
}

.navbar-nav {
    flex-direction: row;
}

.nav-item {
    padding-right: 1rem;
}

.navbar-brand img {
    width: 100px;
}

button {
    border: none;
    padding: 1rem;
    transition: background-color 0.3s ease;
}

#langselect,
#swicher,
.navbar-brand,
.nav-item {
    padding: 0.5rem;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}
